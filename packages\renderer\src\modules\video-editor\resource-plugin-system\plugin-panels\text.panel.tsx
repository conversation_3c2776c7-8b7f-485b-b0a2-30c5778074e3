import React, { memo, useCallback } from 'react'

import { OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'

import { useOverlayHelper } from '@/modules/video-editor/hooks/helpers/useOverlayHelper'

import { StyledTextResource } from '@/types/resources'
import { useFontManager } from '../../hooks/useFontManager'
import {
  TEXT_DEFAULT_CLOUD_FONT_NAME,
  TEXT_DEFAULT_CLOUD_FONT_SRC,
  VIDEO_HEIGHT,
  VIDEO_WIDTH
} from '@/modules/video-editor/constants'

import StyledTextSelector from '@/modules/video-editor/components/common/styled-text-selector'
import { EnhancedTextRenderer } from '@/modules/video-editor/components/common/enhanced-layer-text-renderer'
import { useEditorContext } from '@/modules/video-editor/contexts'

const DEFAULT_TEXT = '默认文字'
const DEFAULT_FONT_SIZE = 150

const TextPanel: React.FC = () => {
  const { calculateTextSize } = useFontManager()
  const { addOverlayToGlobalTrack } = useOverlayHelper()
  const { getPlayerDimensions } = useEditorContext()

  // 创建默认文字覆盖层
  const createDefaultTextOverlay = useCallback(async () => {
    try {
      let overlayWidth = VIDEO_WIDTH / 4
      let overlayHeight = VIDEO_HEIGHT / 4

      // 尝试计算文字尺寸
      try {
        const textSize = await calculateTextSize(
          DEFAULT_TEXT,
          DEFAULT_FONT_SIZE,
          TEXT_DEFAULT_CLOUD_FONT_SRC,
          TEXT_DEFAULT_CLOUD_FONT_NAME
        )

        if (textSize) {
          overlayWidth = textSize.width
          overlayHeight = textSize.height
        }
      } catch (sizeError) {
        console.warn('[默认文字] 文字尺寸计算失败，使用默认尺寸:', sizeError)
      }

      const { playerWidth, playerHeight } = getPlayerDimensions()

      const defaultOverlay: TextOverlay = {
        id: Date.now(),
        type: OverlayType.TEXT,
        src: TEXT_DEFAULT_CLOUD_FONT_SRC,
        content: DEFAULT_TEXT,
        left: (playerWidth - overlayWidth) / 2,
        top: (playerHeight - overlayHeight) / 2,
        width: overlayWidth,
        height: overlayHeight,
        durationInFrames: 90,
        from: 0,
        rotation: 0,
        isDragging: false,
        styles: {
          fontSize: DEFAULT_FONT_SIZE,
          fontWeight: 'normal' as const,
          color: '#ffffff',
          fontFamily: TEXT_DEFAULT_CLOUD_FONT_NAME, // 使用本地字体名称
          fontStyle: 'normal' as const,
          underlineEnabled: false,
          textAlign: 'center' as const,
          backgroundColor: 'transparent',
          zIndex: 20,
          // 轮廓样式 - 默认关闭
          strokeEnabled: false,
          strokeWidth: 0,
          strokeColor: '#000000',
          // 阴影样式 - 默认关闭
          shadowEnabled: false,
          shadowDistance: 0,
          shadowAngle: 45,
          shadowBlur: 2,
          shadowColor: '#000000',
          shadowOpacity: 0.5,
          // 其他样式
          backgroundImage: undefined,
          bubbleTextRect: undefined,
        }
      }

      addOverlayToGlobalTrack(defaultOverlay)
    } catch (error) {
      console.error('[默认文字] 创建默认文字覆盖层失败:', error)
    }
  }, [addOverlayToGlobalTrack, calculateTextSize, getPlayerDimensions])

  const handleAddFontStyleOverlay = useCallback(
    async (fontStyleResource: StyledTextResource.StyledText, previewOverlay: TextOverlay) => {
      try {
        // 计算文字的实际尺寸
        const defaultText = '默认文字'
        const defaultFontSize = 150
        const fontPath = fontStyleResource.content.fontPath
        const fontName = fontStyleResource.content.fontName

        let calculatedWidth = VIDEO_WIDTH / 4
        let calculatedHeight = VIDEO_HEIGHT / 4

        try {
          const textSize = await calculateTextSize(defaultText, defaultFontSize, fontPath, fontName)
          if (textSize) {
            calculatedWidth = textSize.width
            calculatedHeight = textSize.height
          }
        } catch (sizeError) {
          console.warn('[花体字选择] 文字尺寸计算失败，使用默认尺寸:', sizeError)
        }

        // 使用预览覆盖层作为基础，但更新尺寸和位置
        const finalOverlay: TextOverlay = {
          ...previewOverlay,
          id: fontStyleResource.id,
          type: OverlayType.TEXT,
          content: defaultText,
          left: 100,
          top: 100,
          width: calculatedWidth,
          height: calculatedHeight,
          durationInFrames: 90,
          from: 0,
          rotation: 0,
          isDragging: false,
          styles: {
            ...previewOverlay.styles,
            fontSize: defaultFontSize,
          }
        }

        addOverlayToGlobalTrack(finalOverlay)
      } catch (error) {
        console.error('[花体字选择] 创建花体字覆盖层失败:', error)
      }
    },
    [addOverlayToGlobalTrack, calculateTextSize]
  )

  // 渲染默认文字预设项
  const renderDefaultTextItem = () => {
    const defaultPreviewOverlay: TextOverlay = {
      id: 0,
      type: OverlayType.TEXT,
      src: TEXT_DEFAULT_CLOUD_FONT_SRC,
      content: '默认',
      left: 0,
      top: 0,
      width: 80,
      height: 80,
      durationInFrames: 90,
      from: 0,
      rotation: 0,
      isDragging: false,
      styles: {
        fontSize: 28,
        fontWeight: 'normal' as const,
        color: '#ffffff',
        fontFamily: TEXT_DEFAULT_CLOUD_FONT_NAME,
        fontStyle: 'normal' as const,
        underlineEnabled: false,
        textAlign: 'center' as const,
        backgroundColor: 'transparent',
        zIndex: 20,
        strokeEnabled: false,
        strokeWidth: 0,
        strokeColor: '#000000',
        shadowEnabled: false,
        shadowDistance: 0,
        shadowAngle: 45,
        shadowBlur: 2,
        shadowColor: '#000000',
        shadowOpacity: 0.5,
        backgroundImage: undefined,
        bubbleTextRect: undefined,
      }
    }

    const containerStyle: React.CSSProperties = {
      width: '100%',
      height: '100%',
    }

    return (
      <div
        key="default-text"
        onClick={createDefaultTextOverlay}
        className="group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all dark:hover:border-white/20 hover:border-blue-500/80 cursor-pointer aspect-square w-20"
      >
        <div className="h-full w-full flex items-center justify-center rounded">
          <div className="text-base transform-gpu transition-transform group-hover:scale-102 dark:text-white text-gray-900/90 size-full flex justify-center items-center">
            <EnhancedTextRenderer
              overlay={defaultPreviewOverlay}
              containerStyle={containerStyle}
              isPreview={true}
            />
          </div>
        </div>

        {/* Label */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">
          默认文字
        </div>
      </div>
    )
  }

  return (
    <div className="h-full">
      <div className="flex flex-wrap gap-3 p-2">
        {/* 默认文字预设 */}
        {renderDefaultTextItem()}
      </div>

      {/* 花体字选择器 */}
      <StyledTextSelector
        onStyledTextSelect={handleAddFontStyleOverlay}
        className="h-full"
      />
    </div>
  )
}

export default memo(TextPanel)
