import React, { memo, useCallback } from 'react'

import { TextOverlay } from '@clipnest/remotion-shared/types'
import { ResourceType } from '@app/shared/types/resource-cache.types'

import { useInfiniteQueryFontStyleList } from '@/hooks/queries/useQueryTextStyle'

import { CloudResourceTypes, StyledTextResource } from '@/types/resources'
import { cacheManager } from '@/libs/cache/cache-manager'

import InfiniteResourceList from '@/components/InfiniteResourceList'
import { EnhancedTextRenderer } from '@/modules/video-editor/components/common/enhanced-layer-text-renderer'
import { useResource } from '../../hooks/resource/useResource'
import { useFontManager } from '../../hooks/useFontManager'
import { useResourceLoadingStore } from '../../hooks/resource/useResourceLoadingStore'
import { EditorDraggableTypes, useTypedDraggable } from '@/modules/video-editor/components/editor-dnd-wrapper'
import { buildTextOverlay } from '../../utils/text'

interface FontStyleSelectorProps {
  onStyledTextSelect: (item: StyledTextResource.StyledText, previewOverlay: TextOverlay) => void
  baseOverlay?: Partial<TextOverlay>
  className?: string
}

const StyledTextItem: React.FC<StyledTextResource.StyledText & { onSelected: (overlay: TextOverlay) => void }> = ({
  onSelected, ...data
}) => {
  const { setNodeRef, listeners, attributes } = useTypedDraggable(
    EditorDraggableTypes.Resource,
    data.id,
    {
      resourceType: CloudResourceTypes.STYLED_TEXT,
      data
    }
  )

  const { downloadResourceToCache } = useResource()
  const { isFontStyleLoaded } = useFontManager()
  const { isResourceLoading } = useResourceLoadingStore()

  /**
   * 按需下载字体样式资源
   */
  const downloadOnDemand = useCallback(
    async () => {
      try {
        const fontPath = data.content.fontPath

        // 检查字体是否已经下载到本地缓存
        const existingLocalPath = cacheManager.resource.getResourcePathSync(ResourceType.FONT, fontPath)
        if (existingLocalPath) {
          console.debug('[字体下载] 字体已存在于缓存，跳过下载:', data.content.fontName)
          return true
        }

        // 检查是否正在下载
        if (isResourceLoading(fontPath, ResourceType.FONT)) {
          console.debug('[字体下载] 字体正在下载中，跳过:', data.content.fontName)
          return false
        }

        console.debug('[字体下载] 开始下载字体:', data.content.fontName)

        // 下载字体到缓存
        const localPath = await downloadResourceToCache({
          url: fontPath,
          resourceType: ResourceType.FONT,
          version: '1.0'
        })

        if (localPath) {
          console.debug('[字体下载] 字体下载成功:', data.content.fontName)
          return true
        } else {
          console.warn('[字体下载] 字体下载失败:', fontPath)
          return false
        }
      } catch (error) {
        console.error('[字体下载] 字体下载异常:', error)
        return false
      }
    },
    [downloadResourceToCache, isResourceLoading]
  )

  const handleFontStyleSelect = useCallback(
    async () => {
      try {
        // 按需下载/IPC获取本地花体字资源（仅在用户点击使用时）
        const downloadSuccess = await downloadOnDemand()
        if (!downloadSuccess) {
          console.warn('[花体字选择] 花体字资源下载失败，但仍继续处理')
        }

        // 构建预览覆盖层，用于传递给回调函数
        const previewOverlay = buildTextOverlay(data, {
          isPreview: false,
        })

        onSelected(previewOverlay)
      } catch (error) {
        console.error('[花体字选择] 处理花体字选择失败:', error)
      }
    },
    [downloadOnDemand, onSelected]
  )

  const { content, title } = data
  const fontPath = content.fontPath

  const isFontLoaded = isFontStyleLoaded(fontPath)

  const previewOverlay = buildTextOverlay(data, { isPreview: true })

  // 如果字体已加载，更新字体名称
  if (isFontLoaded && content.fontName) {
    previewOverlay.styles.fontFamily = `"${content.fontName}"`
  }

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      <div
        key={data.id.toString()}
        onClick={() => handleFontStyleSelect()}
        className="group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all dark:hover:border-white/20 hover:border-blue-500/80 cursor-pointer aspect-square w-20"
      >
        <div className="h-full w-full flex items-center justify-center rounded">
          <div
            className="text-base transform-gpu transition-transform group-hover:scale-102 dark:text-white text-gray-900/90 size-full flex justify-center items-center"
          >
            <EnhancedTextRenderer
              overlay={previewOverlay}
              containerStyle={{ width: '100%', height: '100%' }}
              isPreview={true}
            />
          </div>
        </div>

        {/* Font Name Label */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">
          {title}
        </div>
      </div>
    </div>
  )
}

const StyledTextSelector: React.FC<FontStyleSelectorProps> = ({
  onStyledTextSelect,
  className = 'h-full'
}) => {
  const { isFontStyleLoaded, loadFont } = useFontManager()
  const { isResourceLoading } = useResourceLoadingStore()

  const infiniteFontStyleQuery = useInfiniteQueryFontStyleList({
    pageNo: 1,
    pageSize: 50
  })

  // 预加载当前页面的所有字体
  React.useEffect(() => {
    const preloadVisibleFonts = async () => {
      if (infiniteFontStyleQuery.data?.pages) {
        const allItems = infiniteFontStyleQuery.data.pages.flatMap(page => page.list || [])

        // 限制预加载数量，防止无限加载
        const maxPreloadCount = 10
        let processedCount = 0

        // 过滤出需要加载的字体
        const fontsToLoad = allItems.filter(item => {
          const { content } = item
          const fontPath = content.fontPath

          if (!content.fontName || !fontPath) {
            return false
          }

          // 检查字体是否已经在 DOM 中加载
          const isFontLoaded = isFontStyleLoaded(fontPath)
          if (isFontLoaded) {
            return false
          }

          // 检查字体是否正在加载中
          const isLoading = isResourceLoading(fontPath, ResourceType.FONT)
          if (isLoading) {
            return false
          }

          // 检查字体是否已经下载到本地缓存
          const localPath = cacheManager.resource.getResourcePathSync(ResourceType.FONT, fontPath)
          if (localPath) {
            // 如果已经下载但未加载到 DOM，只需要加载到 DOM
            console.debug('[字体预加载] 字体已下载，仅需加载到 DOM:', content.fontName)
            return true
          }

          // 字体未下载，需要下载并加载
          return true
        }).slice(0, maxPreloadCount) // 限制预加载数量

        console.debug(`[字体预加载] 需要处理 ${fontsToLoad.length} 个字体 (最大 ${maxPreloadCount} 个)`)

        // 逐个处理字体，避免并发下载
        for (const item of fontsToLoad) {
          if (processedCount >= maxPreloadCount) {
            // console.debug('[字体预加载] 已达到最大预加载数量，停止预加载')
            break
          }

          const { content } = item
          const fontPath = content.fontPath

          try {
            await loadFont(fontPath, content.fontName)
            console.debug('[字体预加载] 字体处理成功:', content.fontName)
            processedCount++
          } catch (error) {
            console.warn('[字体预加载] 字体处理失败:', content.fontName, error)
          }
        }
      }
    }

    void preloadVisibleFonts()
  }, [infiniteFontStyleQuery.data, isFontStyleLoaded, loadFont, isResourceLoading])

  return (
    <div className={className}>
      <InfiniteResourceList
        queryResult={infiniteFontStyleQuery}
        renderItem={item => <StyledTextItem {...item} onSelected={overlay => onStyledTextSelect(item, overlay)} />}
        emptyText="没有找到花体字样式"
        loadingText="加载花体字样式中..."
        itemsContainerClassName="flex flex-wrap gap-3 p-2"
      />
    </div>
  )
}

export default memo(StyledTextSelector)
