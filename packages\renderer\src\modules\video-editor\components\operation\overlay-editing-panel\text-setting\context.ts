import React from 'react'
import { OverlayUpdater } from '@/modules/video-editor/contexts'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import opentype from 'opentype.js'

type TextSettingContextValues = {
  textOverlay: TextOverlay
  requestUpdateText(
    update: OverlayUpdater<TextOverlay>,
    commit?: boolean,
    font?: opentype.Font
  ): void
}

export const TextSettingContext = React.createContext<TextSettingContextValues>(null as any)

export const useTextSettingContext = () => React.useContext(TextSettingContext)
